<script lang="ts">
 import { onMount } from 'svelte';
 import { tweened } from 'svelte/motion';
 import { cubicInOut } from 'svelte/easing';
 import { cn } from '$lib/utils';

 let { className } = $props<{ className?: string }>();

 // Motion Instance 1 (Right Conic)
 let motion1Ref = $state<HTMLDivElement | null>(null);
 const opacity1 = tweened(0.8, { duration: 1000, delay: 100, easing: cubicInOut });
 const width1 = tweened(25, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 // Motion Instance 2 (Left Conic)
 let motion2Ref = $state<HTMLDivElement | null>(null);
 const opacity2 = tweened(0.8, { duration: 1000, delay: 100, easing: cubicInOut });
 const width2 = tweened(25, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 // Motion Instance 3 (Blurry Oval)
 let motion3Ref = $state<HTMLDivElement | null>(null);
 const width3 = tweened(20, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 // Motion Instance 4 (Horizontal Line)
 let motion4Ref = $state<HTMLDivElement | null>(null);
 const width4 = tweened(25, { duration: 1000, delay: 100, easing: cubicInOut }); // in rem

 onMount(() => {
 	// Start animations immediately for background effect with more subtle visibility
 	opacity1.set(0.4);
 	width1.set(35);
 	opacity2.set(0.4);
 	width2.set(35);
 	width3.set(30);
 	width4.set(35);
 });
</script>

<!-- Fixed positioned background lamp effect -->
<div
	class={cn(
		'fixed inset-0 w-screen h-screen overflow-hidden pointer-events-none',
		className
	)}
	style="z-index: 5;"
>
 <!-- Main lamp container with natural circular fade -->
 <div class="absolute inset-0 w-full h-full">

   <!-- Primary radial glow - more visible -->
   <div
   	class="absolute -top-32 left-1/2 -translate-x-1/2 w-[120rem] h-[80rem] rounded-full"
   	style="background: radial-gradient(ellipse at center top, rgba(6, 182, 212, 0.25) 0%, rgba(6, 182, 212, 0.15) 20%, rgba(6, 182, 212, 0.08) 40%, rgba(6, 182, 212, 0.04) 60%, transparent 80%); filter: blur(100px);"
   ></div>

   <!-- Secondary glow for depth -->
   <div
   	class="absolute -top-16 left-1/2 -translate-x-1/2 w-[80rem] h-[60rem] rounded-full"
   	style="background: radial-gradient(ellipse at center top, rgba(6, 182, 212, 0.2) 0%, rgba(6, 182, 212, 0.12) 25%, rgba(6, 182, 212, 0.06) 50%, transparent 75%); filter: blur(60px);"
   ></div>

   <!-- Animated directional beams with better visibility -->
   <div
   	bind:this={motion1Ref}
   	class="absolute top-0 left-1/2 -translate-x-1/2 w-[60rem] h-[40rem] rounded-full"
   	style="opacity: {$opacity1 * 0.8}; background: radial-gradient(ellipse at center top, rgba(6, 182, 212, 0.3) 0%, rgba(6, 182, 212, 0.15) 30%, transparent 60%); transform: rotate(-15deg); filter: blur(40px);"
   ></div>

   <div
   	bind:this={motion2Ref}
   	class="absolute top-0 left-1/2 -translate-x-1/2 w-[60rem] h-[40rem] rounded-full"
   	style="opacity: {$opacity2 * 0.8}; background: radial-gradient(ellipse at center top, rgba(6, 182, 212, 0.25) 0%, rgba(6, 182, 212, 0.12) 35%, transparent 65%); transform: rotate(15deg); filter: blur(50px);"
   ></div>

   <!-- Center focal point - more prominent -->
   <div
   	bind:this={motion3Ref}
   	class="absolute top-2 left-1/2 -translate-x-1/2 rounded-full"
   	style="width: {$width3}rem; height: {$width3 * 0.4}rem; background: radial-gradient(ellipse at center, rgba(6, 182, 212, 0.5) 0%, rgba(6, 182, 212, 0.25) 50%, transparent 100%); filter: blur(15px);"
   ></div>

   <!-- Horizontal accent line - more visible -->
   <div
   	bind:this={motion4Ref}
   	class="absolute top-6 left-1/2 -translate-x-1/2 h-0.5 rounded-full"
   	style="width: {$width4}rem; background: linear-gradient(to right, transparent 0%, rgba(6, 182, 212, 0.6) 20%, rgba(6, 182, 212, 0.8) 50%, rgba(6, 182, 212, 0.6) 80%, transparent 100%); filter: blur(2px);"
   ></div>

   <!-- Natural bottom fade -->
   <div
   	class="absolute bottom-0 left-0 w-full h-64"
   	style="background: linear-gradient(to top, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 50%, transparent 100%);"
   ></div>
 </div>
</div>