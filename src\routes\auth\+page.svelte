<script lang="ts">
import Input from "$lib/components/ui/Input.svelte";
import Label from "$lib/components/ui/Label.svelte";
import { Button } from "$lib/components/ui/button";
import BackgroundBeams from "$lib/components/ui/BackgroundBeams.svelte";
import AuroraBG from "$lib/components/ui/AuroraBG.svelte";
import Logo from "$lib/components/ui/Logo.svelte";
import * as Select from "$lib/components/ui/select";
import { page } from '$app/stores';
import { goto } from '$app/navigation';
import { logger } from '$lib/utils';
import { Alert, AlertTitle, AlertDescription } from '$lib/components/ui/alert';
import { onMount } from "svelte";
import Sun from "lucide-svelte/icons/sun";
import Moon from "lucide-svelte/icons/moon";
import { toggleMode } from "mode-watcher";
import { Button as ShadButton } from "$lib/components/ui/button/index.js";
// Services
import { ALLOWED_DOMAINS } from "$lib/config";
import { auth } from '$lib/services/firebase.config.js';
import { createUserWithEmailAndPassword, signInWithEmailAndPassword, sendPasswordResetEmail } from 'firebase/auth';

let mode: 'login' | 'signup' | 'reset' = 'login';
page.subscribe(($page) => {
	const urlMode = $page.url.searchParams.get('mode');
	if (urlMode === 'signup' || urlMode === 'reset' || urlMode === 'login') {
		mode = urlMode;
		logger.info({ event: 'auth_mode_change', mode });
	} else {
		mode = 'login';
	}
});

let error = '';
let loading = false;
let showSignupAlert = false;

async function handleSubmit(e: Event) {
	e.preventDefault();
	loading = true;
	error = '';
	showSignupAlert = false;
	const form = e.target as HTMLFormElement;
	const formData = new FormData(form);
	const email = (formData.get('email') as string || '').trim();
	const password = (formData.get('password') as string || '').trim();
	const confirmPassword = (formData.get('confirmPassword') as string || '').trim();
	const firstName = (formData.get('firstname') as string || '').trim();
	const lastName = (formData.get('lastname') as string || '').trim();
	const domain = (email.split('@')[1] || '').toLowerCase().trim();
	logger.info({ event: 'domain_check', email, extractedDomain: domain, allowedDomains: ALLOWED_DOMAINS.map(d => d.value) });
	if (!domain || !ALLOWED_DOMAINS.some(d => domain.endsWith(d.value))) {
		logger.error({ event: 'domain_check_failed', email, extractedDomain: domain, allowedDomains: ALLOWED_DOMAINS.map(d => d.value) });
		error = 'Please use an allowed domain to sign up or log in.';
		loading = false;
		return;
	}
	try {
		if (mode === 'login') {
			await signInWithEmailAndPassword(auth, email, password);
			logger.info({ event: 'auth_success', mode, email });
			goto('/chat');
		} else if (mode === 'signup') {
			if (password !== confirmPassword) {
				error = 'Passwords do not match.';
				loading = false;
				return;
			}
			await createUserWithEmailAndPassword(auth, email, password);
			showSignupAlert = true;
			logger.info({ event: 'signup_success', email });
			setTimeout(() => {
				showSignupAlert = false;
				setMode('login');
			}, 2500);
		} else if (mode === 'reset') {
			await sendPasswordResetEmail(auth, email);
			error = 'Password reset link sent. Please check your email.';
		}
	} catch (err) {
		if (typeof err === 'object' && err !== null && 'message' in err && typeof (err as any).message === 'string') {
			error = (err as { message: string }).message;
		} else {
			error = 'Authentication error.';
		}
		logger.error({ event: 'auth_error', mode, email, error });
	} finally {
		loading = false;
	}
}

function setMode(newMode: 'login' | 'signup' | 'reset') {
	goto(`/auth?mode=${newMode}`);
}

onMount(() => {
	const isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
	const html = document.documentElement;
	if (isSystemDark) {
		html.classList.add('dark');
	} else {
		html.classList.remove('dark');
	}
});
</script>

<div class="min-h-screen relative">
	<!-- Theme toggle button -->
	<ShadButton on:click={toggleMode} variant="outline" size="icon" class="absolute top-4 right-4 z-30">
		<Sun class="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
		<Moon class="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
		<span class="sr-only">Toggle theme</span>
	</ShadButton>

	<!-- Aurora background behind everything -->
	<div class="absolute inset-0 w-full h-full z-0">
		<AuroraBG className="w-full h-full" />
	</div>

	<!-- Transparent background image on top of aurora -->
	<div class="absolute inset-0 w-full h-full z-10">
		<img src="/img/vs_transparent.png" alt="Background" class="w-full h-full object-cover object-center" />
	</div>

	<!-- Auth Form Container -->
	<div class="relative z-20 flex flex-col justify-center items-center w-full min-h-screen">
		<!-- Logo positioned above the form, visible on wider screens -->
		<div class="hidden md:block mb-8 mt-[35vh] lg:mt-[40vh] xl:mt-[45vh]">
			<Logo size="large" variant="light" className="drop-shadow-lg" />
		</div>

		<!-- Responsive positioning to align with scooter headlight -->
		<div class="w-full max-w-md mx-auto px-4 mt-[40vh] md:mt-8 lg:mt-12 xl:mt-16">
			<div class="relative w-full rounded-2xl border border-gray-300 bg-white p-8 shadow dark:border-gray-800 dark:bg-black">
				<h1 class="text-3xl font-bold mb-2 text-neutral-800 dark:text-neutral-200 text-center">
					{mode === 'login' ? 'Welcome Back' : mode === 'signup' ? 'Create an Account' : 'Reset Password'}
				</h1>
				<p class="text-gray-600 dark:text-neutral-400 text-center mb-6">
					{mode === 'login' ? 'Sign in to access Vahan Sahayak' : mode === 'signup' ? 'Sign up to get started with Vahan Sahayak' : 'Enter your email to reset your password'}
				</p>

				{#if error}
					<div class="bg-red-50 border border-red-200 text-red-800 rounded-md p-3 mb-4">{error}</div>
				{/if}

				{#if showSignupAlert && mode === 'signup'}
					<Alert class="mb-4">
						<AlertTitle>Check your email</AlertTitle>
						<AlertDescription>
							A verification link has been sent to your email address. Please verify to continue.
						</AlertDescription>
					</Alert>
				{/if}

				<form class="space-y-4" on:submit={handleSubmit}>
					<div>
						<Label htmlFor="email">Email Address</Label>
						<Input id="email" name="email" placeholder="<EMAIL>" type="email" className="w-full" required />
					</div>
					{#if mode !== 'reset'}
						<div>
							<Label htmlFor="password">Password</Label>
							<Input id="password" name="password" placeholder="••••••••" type="password" className="w-full" required />
						</div>
						{#if mode === 'signup'}
							<div class="flex mb-4 space-x-2">
								<div class="flex-1">
									<Label htmlFor="firstname">First name</Label>
									<Input id="firstname" name="firstname" placeholder="Tyler" type="text" className="w-full" />
								</div>
								<div class="flex-1">
									<Label htmlFor="lastname">Last name</Label>
									<Input id="lastname" name="lastname" placeholder="Durden" type="text" className="w-full" />
								</div>
							</div>
							<div>
								<Label htmlFor="confirmPassword">Confirm Password</Label>
								<Input id="confirmPassword" name="confirmPassword" placeholder="••••••••" type="password" className="w-full" required />
							</div>
						{/if}
					{/if}
					<Button type="submit" class="w-full" disabled={loading}>
						{loading ? 'Processing...' : mode === 'login' ? 'Sign In' : mode === 'signup' ? 'Sign Up' : 'Send Reset Link'}
					</Button>
				</form>

				<div class="mt-6 text-center space-y-2">
					{#if mode === 'login'}
						<button class="text-blue-600 hover:underline text-sm" on:click={() => setMode('signup')} disabled={loading}>Don't have an account? Sign up</button>
						<br />
						<button class="text-gray-500 hover:underline text-xs" on:click={() => setMode('reset')} disabled={loading}>Forgot password?</button>
					{:else if mode === 'signup'}
						<button class="text-blue-600 hover:underline text-sm" on:click={() => setMode('login')} disabled={loading}>Already have an account? Sign in</button>
					{:else}
						<button class="text-blue-600 hover:underline text-sm" on:click={() => setMode('login')} disabled={loading}>Back to login</button>
					{/if}
				</div>
			</div>
		</div>
	</div>
</div>
